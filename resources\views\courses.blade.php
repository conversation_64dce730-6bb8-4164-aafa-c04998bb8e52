@extends('layouts.app')

@section('title', $seoData['title'] ?? 'Kursus Teknologi Terbaik - Ngambiskuy')

@push('styles')
<meta name="description" content="{{ $seoData['description'] ?? 'Temukan kursus teknologi terbaik untuk mengembangkan skill programming, data science, AI, dan web development.' }}">
<meta name="keywords" content="{{ $seoData['keywords'] ?? 'kursus programming, belajar coding, kursus teknologi' }}">
<meta name="robots" content="index, follow">
<link rel="canonical" href="{{ $seoData['canonical'] ?? request()->url() }}">

<!-- Open Graph / Facebook -->
<meta property="og:type" content="website">
<meta property="og:url" content="{{ request()->url() }}">
<meta property="og:title" content="{{ $seoData['title'] ?? 'Kursus Teknologi Terbaik - Ngambiskuy' }}">
<meta property="og:description" content="{{ $seoData['description'] ?? 'Temukan kursus teknologi terbaik untuk mengembangkan skill programming, data science, AI, dan web development.' }}">
<meta property="og:image" content="{{ asset('images/og-courses.jpg') }}">

<!-- Twitter -->
<meta property="twitter:card" content="summary_large_image">
<meta property="twitter:url" content="{{ request()->url() }}">
<meta property="twitter:title" content="{{ $seoData['title'] ?? 'Kursus Teknologi Terbaik - Ngambiskuy' }}">
<meta property="twitter:description" content="{{ $seoData['description'] ?? 'Temukan kursus teknologi terbaik untuk mengembangkan skill programming, data science, AI, dan web development.' }}">
<meta property="twitter:image" content="{{ asset('images/og-courses.jpg') }}">

<!-- Structured Data -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "EducationalOrganization",
  "name": "Ngambiskuy",
  "description": "Platform pembelajaran teknologi bertenaga AI untuk mengembangkan skill programming dan teknologi",
  "url": "{{ url('/') }}",
  "logo": "{{ asset('images/logo.png') }}",
  "sameAs": [
    "https://www.facebook.com/ngambiskuy",
    "https://www.instagram.com/ngambiskuy",
    "https://www.linkedin.com/company/ngambiskuy"
  ]
}
</script>
@endpush

@section('content')
<!-- Hero Section -->
<section class="bg-gradient-to-br from-orange-50 via-white to-orange-100 py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <!-- Badge -->
            {{-- <div class="inline-flex items-center px-4 py-2 rounded-full bg-primary/10 text-primary text-sm font-medium mb-6">
                🎯 Kurikulum Berstandar Industri
            </div> --}}

            <!-- Main Heading -->
            <h1 class="text-4xl lg:text-6xl font-bold text-gray-900 leading-tight mb-6">
                Kursus Teknologi
                <span class="text-transparent bg-clip-text bg-gradient-to-r from-primary to-red-500">
                    Terdepan
                </span>
            </h1>

            <!-- Subtitle -->
            <p class="text-xl text-gray-600 leading-relaxed max-w-3xl mx-auto mb-8">
                Kuasai skill teknologi masa depan dengan kurikulum yang dirancang khusus untuk profesional Indonesia.
                Dari pemula hingga expert, temukan jalur pembelajaran yang tepat untuk karir impian Anda.
            </p>

            <!-- CTA Buttons -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center mb-16">
                <a href="#courses" class="btn btn-primary btn-lg">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    Jelajahi Kursus
                </a>
                <a href="{{ route('courses.index', ['price_type' => 'free']) }}" class="btn btn-outline btn-lg">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                    </svg>
                    Mulai Gratis
                </a>
            </div>

            <!-- Stats -->
            {{-- <div class="bg-white rounded-2xl p-8 shadow-lg">
                <div class="grid grid-cols-2 lg:grid-cols-4 gap-8 text-center">
                    <div>
                        <div class="text-3xl font-bold text-gray-900 mb-2">{{ number_format($stats['total_courses']) }}</div>
                        <div class="text-gray-600">Kursus Berkualitas</div>
                    </div>
                    <div>
                        <div class="text-3xl font-bold text-green-600 mb-2">{{ number_format($stats['free_courses']) }}</div>
                        <div class="text-gray-600">Kursus Gratis</div>
                    </div>
                    <div>
                        <div class="text-3xl font-bold text-blue-600 mb-2">{{ number_format($stats['paid_courses']) }}</div>
                        <div class="text-gray-600">Kursus Premium</div>
                    </div>
                    <div>
                        <div class="text-3xl font-bold text-purple-600 mb-2">{{ number_format($stats['total_students']) }}+</div>
                        <div class="text-gray-600">Siswa Aktif</div>
                    </div>
                </div>
            </div> --}}
        </div>
    </div>
</section>

<!-- Filters Section -->
<section class="bg-white py-8 border-b" id="courses">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Section Header -->
        <div class="text-center mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-2">Temukan Kursus yang Tepat</h2>
            <p class="text-gray-600">Gunakan filter di bawah untuk menemukan kursus sesuai kebutuhan Anda</p>
        </div>

        <!-- Filter Form -->
        <form method="GET" action="{{ route('courses.index') }}" class="space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <!-- Search -->
                <div class="space-y-2">
                    <label for="search" class="block text-sm font-medium text-gray-700">Cari Kursus</label>
                    <div class="relative">
                        <input type="text"
                               id="search"
                               name="search"
                               value="{{ request('search') }}"
                               placeholder="Masukkan kata kunci..."
                               class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                    </div>
                </div>

                <!-- Category Filter -->
                <div class="space-y-2">
                    <label for="category" class="block text-sm font-medium text-gray-700">Kategori</label>
                    <select id="category" name="category" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors">
                        <option value="">Semua Kategori</option>
                        @foreach($categories as $category)
                            <option value="{{ $category->slug }}" {{ request('category') == $category->slug ? 'selected' : '' }}>
                                {{ $category->name }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <!-- Level Filter -->
                <div class="space-y-2">
                    <label for="level" class="block text-sm font-medium text-gray-700">Level Kesulitan</label>
                    <select id="level" name="level" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors">
                        <option value="">Semua Level</option>
                        <option value="beginner" {{ request('level') == 'beginner' ? 'selected' : '' }}>🟢 Pemula</option>
                        <option value="intermediate" {{ request('level') == 'intermediate' ? 'selected' : '' }}>🟡 Menengah</option>
                        <option value="advanced" {{ request('level') == 'advanced' ? 'selected' : '' }}>🔴 Lanjutan</option>
                    </select>
                </div>

                <!-- Price Filter -->
                <div class="space-y-2">
                    <label for="price_type" class="block text-sm font-medium text-gray-700">Harga</label>
                    <select id="price_type" name="price_type" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors">
                        <option value="">Semua Harga</option>
                        <option value="free" {{ request('price_type') == 'free' ? 'selected' : '' }}>💚 Gratis</option>
                        <option value="paid" {{ request('price_type') == 'paid' ? 'selected' : '' }}>💎 Premium</option>
                    </select>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex flex-col sm:flex-row gap-3 justify-center">
                <button type="submit" class="btn btn-primary btn-lg">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    Cari Kursus
                </button>
                @if(request()->hasAny(['search', 'category', 'level', 'price_type']))
                    <a href="{{ route('courses.index') }}" class="btn btn-outline btn-lg">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                        Reset Filter
                    </a>
                @endif
            </div>
        </form>
    </div>
</section>

<!-- Courses Grid -->
<section class="bg-gray-50 py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        @if($courses->count() > 0)
            <!-- Results Header -->
            <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8">
                <div>
                    <h2 class="text-2xl font-bold text-gray-900 mb-2">
                        {{ $courses->total() }} Kursus Ditemukan
                    </h2>
                    <p class="text-gray-600">
                        @if(request()->hasAny(['search', 'category', 'level', 'price_type']))
                            Hasil pencarian untuk filter yang dipilih
                        @else
                            Semua kursus tersedia di platform kami
                        @endif
                    </p>
                </div>

                <!-- Sort Options -->
                <div class="mt-4 sm:mt-0">
                    <select onchange="window.location.href=this.value" class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option value="{{ request()->fullUrlWithQuery(['sort' => 'newest']) }}" {{ request('sort') == 'newest' ? 'selected' : '' }}>Terbaru</option>
                        <option value="{{ request()->fullUrlWithQuery(['sort' => 'popular']) }}" {{ request('sort') == 'popular' ? 'selected' : '' }}>Terpopuler</option>
                        <option value="{{ request()->fullUrlWithQuery(['sort' => 'rating']) }}" {{ request('sort') == 'rating' ? 'selected' : '' }}>Rating Tertinggi</option>
                        <option value="{{ request()->fullUrlWithQuery(['sort' => 'price_low']) }}" {{ request('sort') == 'price_low' ? 'selected' : '' }}>Harga Terendah</option>
                    </select>
                </div>
            </div>

            <!-- Courses Grid -->
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                @foreach($courses as $course)
                    <div class="bg-white rounded-xl shadow-sm hover:shadow-xl transition-all duration-300 overflow-hidden group">
                        <div class="relative">
                            @if($course->thumbnail)
                                <img src="{{ asset('storage/' . $course->thumbnail) }}" alt="{{ $course->title }}" class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300">
                            @else
                                <div class="w-full h-48 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                                    <svg class="w-16 h-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                    </svg>
                                </div>
                            @endif

                            <!-- Price Badge -->
                            @if($course->price == 0)
                                <span class="absolute top-3 right-3 bg-green-600 text-white text-xs font-medium px-3 py-1 rounded-full">
                                    💚 GRATIS
                                </span>
                            @else
                                <span class="absolute top-3 right-3 bg-primary text-white text-xs font-medium px-3 py-1 rounded-full">
                                    {{ $course->formatted_price }}
                                </span>
                            @endif

                            <!-- Featured Badge -->
                            @if($course->is_featured)
                                <span class="absolute top-3 left-3 bg-yellow-500 text-white text-xs font-medium px-3 py-1 rounded-full">
                                    ⭐ FEATURED
                                </span>
                            @endif

                            <!-- Completion Badge (if user is enrolled and completed) -->
                            @auth
                                @if(auth()->user()->hasCompletedCourse($course->id ?? 0))
                                    <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-green-600 text-white rounded-full p-3">
                                        <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                @endif
                            @endauth
                        </div>

                        <div class="p-6">
                            <div class="space-y-4">
                                <!-- Category & Level -->
                                <div class="flex items-center justify-between">
                                    <span class="inline-block bg-primary/10 text-primary text-xs font-medium px-3 py-1 rounded-full">
                                        {{ $course->category->name }}
                                    </span>
                                    <span class="text-xs font-medium px-2 py-1 rounded-full
                                        {{ $course->level == 'beginner' ? 'bg-green-100 text-green-800' : '' }}
                                        {{ $course->level == 'intermediate' ? 'bg-yellow-100 text-yellow-800' : '' }}
                                        {{ $course->level == 'advanced' ? 'bg-red-100 text-red-800' : '' }}">
                                        {{ $course->level_indonesian }}
                                    </span>
                                </div>

                                <!-- Title -->
                                <h3 class="text-lg font-bold text-gray-900 line-clamp-2 group-hover:text-primary transition-colors">
                                    {{ $course->title }}
                                </h3>

                                <!-- Description -->
                                <p class="text-gray-600 text-sm line-clamp-2 leading-relaxed">
                                    {{ $course->description }}
                                </p>

                                <!-- Instructor -->
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-gradient-to-br from-primary/20 to-primary/10 rounded-full flex items-center justify-center">
                                        <svg class="w-4 h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">{{ $course->tutor->name }}</p>
                                        <p class="text-xs text-gray-500">Instruktur</p>
                                    </div>
                                </div>

                                <!-- Course Stats -->
                                <div class="grid grid-cols-2 gap-4 py-3 border-t border-gray-100">
                                    <div class="flex items-center space-x-2">
                                        <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        <span class="text-sm text-gray-600">{{ $course->duration ?? '4 jam' }}</span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                        </svg>
                                        <span class="text-sm text-gray-600">{{ number_format($course->total_students ?? 0) }} siswa</span>
                                    </div>
                                </div>

                                <!-- Rating -->
                                @if(($course->average_rating ?? 0) > 0)
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center space-x-1">
                                            <div class="flex items-center">
                                                @for($i = 1; $i <= 5; $i++)
                                                    <svg class="w-4 h-4 {{ $i <= ($course->average_rating ?? 0) ? 'text-yellow-400' : 'text-gray-300' }}" fill="currentColor" viewBox="0 0 20 20">
                                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                                    </svg>
                                                @endfor
                                            </div>
                                            <span class="text-sm font-medium text-gray-900">{{ number_format($course->average_rating ?? 0, 1) }}</span>
                                        </div>
                                        <span class="text-xs text-gray-500">({{ number_format($course->total_reviews ?? 0) }} ulasan)</span>
                                    </div>
                                @endif

                                <!-- Action Button -->
                                <div class="pt-2">
                                    <a href="{{ route('course.show.v2', $course) }}" class="btn btn-primary w-full group-hover:bg-primary-dark transition-colors">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                        </svg>
                                        Lihat Detail
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Pagination -->
            <div class="mt-16">
                <div class="flex justify-center">
                    {{ $courses->links() }}
                </div>
            </div>
        @else
            <!-- No Courses Found -->
            <div class="text-center py-20">
                <div class="max-w-md mx-auto">
                    <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                        <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-3">Tidak Ada Kursus Ditemukan</h3>
                    <p class="text-gray-600 mb-8 leading-relaxed">
                        Maaf, tidak ada kursus yang sesuai dengan kriteria pencarian Anda.
                        Coba ubah filter atau kata kunci untuk menemukan kursus yang tepat.
                    </p>
                    <div class="space-y-3">
                        <a href="{{ route('courses.index') }}" class="btn btn-primary btn-lg">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                            Lihat Semua Kursus
                        </a>
                        <div class="text-sm text-gray-500">
                            atau <a href="{{ route('courses.index', ['price_type' => 'free']) }}" class="text-primary hover:underline">coba kursus gratis</a>
                        </div>
                    </div>
                </div>
            </div>
        @endif
    </div>
</section>

<!-- Call to Action Section -->
<section class="bg-gradient-to-br from-primary to-red-500 py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div class="max-w-3xl mx-auto">
            <h2 class="text-3xl lg:text-4xl font-bold text-white mb-6">
                Siap Memulai Perjalanan Belajar Anda?
            </h2>
            <p class="text-xl text-white/90 mb-8 leading-relaxed">
                Bergabunglah dengan ribuan profesional yang telah mengembangkan karir mereka bersama Ngambiskuy.
                Mulai hari ini dan raih masa depan yang lebih cerah!
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="{{ route('register') }}" class="btn btn-lg bg-white text-primary hover:bg-gray-100">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
                    </svg>
                    Daftar Sekarang
                </a>
                <a href="{{ route('courses.index', ['price_type' => 'free']) }}" class="btn btn-lg border-2 border-white text-white hover:bg-white hover:text-primary">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                    </svg>
                    Coba Gratis
                </a>
            </div>
        </div>
    </div>
</section>
@endsection
